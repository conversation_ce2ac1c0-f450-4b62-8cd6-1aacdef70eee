# Ultralytics YOLO 🚀, AGPL-3.0 license
# YOLOv8 object detection model with P3-P5 outputs. For Usage examples see https://docs.ultralytics.com/tasks/detect

# Parameters
nc: 3  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n.yaml' will call yolov8.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024]  # YOLOv8n summary: 225 layers,  3157200 parameters,  3157184 gradients,   8.9 GFLOPs
  s: [0.33, 0.50, 1024]  # YOLOv8s summary: 225 layers, 11166560 parameters, 11166544 gradients,  28.8 GFLOPs
  m: [0.67, 0.75, 768]   # YOLOv8m summary: 295 layers, 25902640 parameters, 25902624 gradients,  79.3 GFLOPs
  l: [1.00, 1.00, 512]   # YOLOv8l summary: 365 layers, 43691520 parameters, 43691504 gradients, 165.7 GFLOPs
  x: [1.00, 1.25, 512]   # YOLOv8x summary: 365 layers, 68229648 parameters, 68229632 gradients, 258.5 GFLOPs

# YOLOv8.0n backbone
backbone:
  # [from, repeats, module, args]
  # 0 自定义空层，用于保存输入数据
  - [-1, 1, Empty, []]  # 0

  # Yolov8n-teacher的主干网络

  - [-1, 1, Conv, [64, 3, 2]]  # 1-P1/2
  - [-1, 1, Conv, [128, 3, 2]]  # 2-P2/4
  - [-1, 3, C2f, [128, True]] # 3
  - [-1, 1, Conv, [256, 3, 2]]  # 4-P3/8
  - [-1, 6, C2f, [256, True]]  #5
  - [-1, 1, Conv, [512, 3, 2]]  # 6-P4/16
  - [-1, 6, C2f, [512, True]] # 7
  - [-1, 1, Conv, [1024, 3, 2]]  # 8-P5/32
  - [-1, 3, C2f, [1024, True]] # 9
  - [-1, 1, SPPF, [1024, 5]]  # 10

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 11
  - [[-1, 7], 1, Concat, [1]]  # cat backbone P4 # 12
  - [-1, 3, C2f, [512]]  # 13
  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 14
  - [[-1, 5], 1, Concat, [1]]  # 15 cat backbone P3 
  - [-1, 3, C2f, [256]]  # 16 (P3/8-small)
  - [-1, 1, Conv, [256, 3, 2]] # 17
  - [[-1, 13], 1, Concat, [1]]  # 18 cat head P4
  - [-1, 3, C2f, [512]]  # 19 (P4/16-medium)
  - [-1, 1, Conv, [512, 3, 2]] # 20
  - [[-1, 10], 1, Concat, [1]]  # 21 cat head P5
  - [-1, 3, C2f, [1024]]  # 22 (P5/32-large)

# Yolov8n-student的主干网络

  - [0, 1, Conv, [64, 3, 2]]  # 23-P1/2
  - [-1, 1, Conv, [128, 3, 2]]  # 24-P2/4
  - [-1, 3, C2f, [128, True]] # 25
  - [-1, 1, Conv, [256, 3, 2]]  # 26-P3/8
  - [-1, 6, C2f, [256, True]]  #27
  - [-1, 1, Conv, [512, 3, 2]]  # 28-P4/16
  - [-1, 6, C2f, [512, True]] # 29
  - [-1, 1, Conv, [1024, 3, 2]]  # 30-P5/32
  - [-1, 3, C2f, [1024, True]] # 31
  - [-1, 1, SPPF, [1024, 5]]  # 32

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 33
  - [[-1, 29], 1, Concat, [1]]  # cat backbone P4 # 34
  - [-1, 3, C2f, [512]]  # 35
  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 36
  - [[-1, 27], 1, Concat, [1]]  # 37 cat backbone P3 
  - [-1, 3, C2f, [256]]  # 38 (P3/8-small)
  - [-1, 1, Conv, [256, 3, 2]] # 39
  - [[-1, 35], 1, Concat, [1]]  # 40 cat head P4
  - [-1, 3, C2f, [512]]  # 41 (P4/16-medium)
  - [-1, 1, Conv, [512, 3, 2]] # 42
  - [[-1, 32], 1, Concat, [1]]  # 43 cat head P5
  - [-1, 3, C2f, [1024]]  # 44 (P5/32-large)

  # Yolov8n的head网络
head:
  - [[16, 19, 22], 1, Detect_Teacher, [nc]] # 45
  - [[16, 19, 22], 1, Detect_Student, [nc]] # 46  🔧 特征层对齐：使用与教师相同的层
  - [[45, 46], 1, Detect_Distill, [nc]]  # 47 Detect(P3, P4, P5)



