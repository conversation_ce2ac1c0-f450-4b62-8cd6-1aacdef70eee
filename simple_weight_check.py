#!/usr/bin/env python3
"""
简单的权重文件检查
"""

import torch
import os

def check_weight_file(weight_path):
    """检查单个权重文件"""
    print(f"\n🔍 检查: {weight_path}")
    
    if not os.path.exists(weight_path):
        print(f"❌ 文件不存在")
        return
    
    size_mb = os.path.getsize(weight_path) / (1024*1024)
    print(f"📊 文件大小: {size_mb:.1f}MB")
    
    try:
        checkpoint = torch.load(weight_path, map_location='cpu')
        
        # 获取类别数
        nc = None
        if isinstance(checkpoint, dict) and 'model' in checkpoint:
            model = checkpoint['model']
            if hasattr(model, 'nc'):
                nc = model.nc
                print(f"✅ 类别数: {nc}")
            
            # 检查训练数据集
            if 'train_args' in checkpoint:
                args = checkpoint['train_args']
                if 'data' in args:
                    print(f"📁 训练数据集: {args['data']}")
        
        # 检查是否适合COCO128
        if nc == 80:
            print(f"✅ 适合COCO128数据集 (80类)")
        elif nc == 3:
            print(f"⚠️ 适合水果数据集 (3类)")
        elif nc:
            print(f"⚠️ 其他类别数: {nc}")
        else:
            print(f"❓ 无法确定类别数")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🚀 权重文件快速检查")
    
    # 检查主要权重文件
    weight_files = [
        "yolov8n.pt",
        "yolov8s.pt", 
        "./(f)models/yolov8n.pt",
        "./(f)models/yolov8s.pt",
        "./(f)models/yolov8n-fruit.pt",
        "./(f)models/yolov8s-fruit.pt"
    ]
    
    for weight_file in weight_files:
        check_weight_file(weight_file)
    
    print(f"\n📋 总结:")
    print(f"- yolov8n.pt 和 yolov8s.pt 如果是80类，适合COCO128")
    print(f"- yolov8n-fruit.pt 和 yolov8s-fruit.pt 如果是3类，适合水果数据集")

if __name__ == "__main__":
    main()
