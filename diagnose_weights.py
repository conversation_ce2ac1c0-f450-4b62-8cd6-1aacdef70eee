#!/usr/bin/env python3
"""
诊断权重文件和模型架构不匹配问题
"""

import torch
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append('.')

def analyze_weight_file(weight_path):
    """分析权重文件的结构"""
    print(f"🔍 分析权重文件: {weight_path}")
    
    if not os.path.exists(weight_path):
        print(f"❌ 文件不存在: {weight_path}")
        return None
    
    try:
        # 加载权重文件
        if weight_path.endswith('.pt'):
            checkpoint = torch.load(weight_path, map_location='cpu')
            if isinstance(checkpoint, dict):
                if 'model' in checkpoint:
                    state_dict = checkpoint['model'].state_dict() if hasattr(checkpoint['model'], 'state_dict') else checkpoint['model']
                    print(f"✅ 检测到完整的checkpoint格式")
                    if 'nc' in checkpoint.get('model', {}).__dict__:
                        print(f"📊 模型类别数: {checkpoint['model'].nc}")
                else:
                    state_dict = checkpoint
                    print(f"✅ 检测到state_dict格式")
            else:
                state_dict = checkpoint.state_dict() if hasattr(checkpoint, 'state_dict') else checkpoint
                print(f"✅ 检测到模型对象格式")
        elif weight_path.endswith('.pth'):
            state_dict = torch.load(weight_path, map_location='cpu')
            print(f"✅ 检测到.pth格式")
        else:
            print(f"❌ 不支持的文件格式: {weight_path}")
            return None
        
        print(f"📊 权重层总数: {len(state_dict)}")
        
        # 分析检测头相关层
        detection_layers = []
        for key, tensor in state_dict.items():
            if any(x in key.lower() for x in ['detect', 'cv2', 'cv3', 'head']):
                detection_layers.append((key, tensor.shape))
        
        print(f"\n🎯 检测头相关层 ({len(detection_layers)}个):")
        for key, shape in detection_layers:
            print(f"   {key}: {shape}")
            
        # 分析可能的类别数
        class_related = []
        for key, tensor in state_dict.items():
            if 'cv3' in key and 'weight' in key and len(tensor.shape) == 4:
                # cv3通常是分类头，输出通道数就是类别数
                class_related.append((key, tensor.shape[0]))
        
        if class_related:
            print(f"\n📋 推断的类别数:")
            for key, nc in class_related:
                print(f"   {key}: {nc}类")
        
        return state_dict
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return None

def analyze_model_config(config_path):
    """分析模型配置文件"""
    print(f"\n🔍 分析模型配置: {config_path}")
    
    try:
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        nc = config.get('nc', 'unknown')
        print(f"📊 配置文件类别数: {nc}")
        
        # 分析head结构
        head = config.get('head', [])
        print(f"📊 Head层数: {len(head)}")
        
        for i, layer in enumerate(head):
            print(f"   层{i}: {layer}")
            
        return config
        
    except Exception as e:
        print(f"❌ 配置文件分析失败: {e}")
        return None

def check_compatibility():
    """检查权重和配置的兼容性"""
    print(f"\n🔧 兼容性检查")
    
    # 检查权重文件
    weight_paths = [
        './(f)models/yolov8n-fruit.pt',
        './(f)models/yolov8s-fruit.pt',
        './(f)models/yolov8_teacher_prepare-fruit.pth',
        './(f)models/yolov8_student_prepare-fruit.pth'
    ]
    
    existing_weights = []
    for path in weight_paths:
        if os.path.exists(path):
            existing_weights.append(path)
            print(f"✅ 找到权重文件: {path}")
        else:
            print(f"❌ 权重文件不存在: {path}")
    
    # 分析第一个存在的权重文件
    if existing_weights:
        analyze_weight_file(existing_weights[0])
    
    # 检查配置文件
    config_path = "ultralytics/cfg/models/v8/yolov8-lite-fruit.yaml"
    if os.path.exists(config_path):
        analyze_model_config(config_path)
    else:
        print(f"❌ 配置文件不存在: {config_path}")

def suggest_solutions():
    """建议解决方案"""
    print(f"\n💡 解决方案建议:")
    print(f"1. 检查权重文件的类别数是否与配置文件匹配")
    print(f"2. 如果类别数不匹配，需要:")
    print(f"   - 使用相同类别数的预训练权重")
    print(f"   - 或者转换权重文件的类别数")
    print(f"   - 或者修改模型配置的nc参数")
    print(f"3. 确保教师和学生模型的架构兼容")
    print(f"4. 检查是否有维度不匹配的特征层")

def main():
    """主函数"""
    print("🚀 YOLO知识蒸馏权重诊断工具\n")
    
    check_compatibility()
    suggest_solutions()
    
    print(f"\n📋 诊断完成")

if __name__ == "__main__":
    main()
