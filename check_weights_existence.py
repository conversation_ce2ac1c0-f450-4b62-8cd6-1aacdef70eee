#!/usr/bin/env python3
"""
检查权重文件是否存在以及兼容性
"""

import os
import torch
import glob
from pathlib import Path

def find_weight_files():
    """查找所有权重文件"""
    print("🔍 搜索权重文件...")
    
    # 搜索模式
    patterns = [
        "yolov8n.pt",
        "yolov8s.pt", 
        "yolov8n-*.pt",
        "yolov8s-*.pt",
        "**/yolov8n.pt",
        "**/yolov8s.pt",
        "**/*yolov8n*.pt",
        "**/*yolov8s*.pt"
    ]
    
    found_files = []
    
    for pattern in patterns:
        files = glob.glob(pattern, recursive=True)
        for file in files:
            if os.path.exists(file):
                found_files.append(os.path.abspath(file))
    
    # 去重
    found_files = list(set(found_files))
    
    print(f"📊 找到 {len(found_files)} 个权重文件:")
    for file in found_files:
        size_mb = os.path.getsize(file) / (1024*1024)
        print(f"   ✅ {file} ({size_mb:.1f}MB)")
    
    return found_files

def analyze_weight_file(weight_path):
    """分析权重文件"""
    print(f"\n🔍 分析权重文件: {os.path.basename(weight_path)}")
    
    try:
        # 加载权重
        checkpoint = torch.load(weight_path, map_location='cpu')
        
        # 获取模型信息
        if isinstance(checkpoint, dict):
            if 'model' in checkpoint:
                model = checkpoint['model']
                print(f"✅ 检测到完整checkpoint格式")
                
                # 获取类别数
                if hasattr(model, 'nc'):
                    print(f"📊 类别数: {model.nc}")
                elif hasattr(model, 'model') and hasattr(model.model[-1], 'nc'):
                    print(f"📊 类别数: {model.model[-1].nc}")
                
                # 获取模型任务
                if hasattr(model, 'task'):
                    print(f"🎯 任务类型: {model.task}")
                
                # 获取训练参数
                if 'train_args' in checkpoint:
                    args = checkpoint['train_args']
                    if 'data' in args:
                        print(f"📁 训练数据集: {args['data']}")
                    if 'epochs' in args:
                        print(f"🔄 训练轮数: {args['epochs']}")
                
                # 分析检测头
                state_dict = model.state_dict() if hasattr(model, 'state_dict') else model
                
            else:
                state_dict = checkpoint
                print(f"✅ 检测到state_dict格式")
        else:
            state_dict = checkpoint.state_dict() if hasattr(checkpoint, 'state_dict') else checkpoint
            print(f"✅ 检测到模型对象格式")
        
        # 分析检测头层
        detection_layers = []
        for key, tensor in state_dict.items():
            if 'model.22' in key or 'model.23' in key:  # 通常是检测头
                detection_layers.append((key, tensor.shape))
        
        if detection_layers:
            print(f"🎯 检测头层:")
            for key, shape in detection_layers[:5]:  # 只显示前5个
                print(f"   {key}: {shape}")
                
                # 推断类别数
                if 'cv3' in key and 'weight' in key and len(shape) == 4:
                    nc = shape[0]
                    print(f"   → 推断类别数: {nc}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def check_coco128_compatibility(weight_files):
    """检查与COCO128数据集的兼容性"""
    print(f"\n🔧 检查COCO128兼容性...")
    
    # COCO128是COCO数据集的子集，有80个类别
    expected_nc = 80
    
    compatible_files = []
    
    for weight_file in weight_files:
        print(f"\n检查: {os.path.basename(weight_file)}")
        
        try:
            checkpoint = torch.load(weight_file, map_location='cpu')
            
            # 获取类别数
            nc = None
            if isinstance(checkpoint, dict) and 'model' in checkpoint:
                model = checkpoint['model']
                if hasattr(model, 'nc'):
                    nc = model.nc
                elif hasattr(model, 'model') and hasattr(model.model[-1], 'nc'):
                    nc = model.model[-1].nc
            
            if nc == expected_nc:
                print(f"✅ 兼容COCO128 (类别数: {nc})")
                compatible_files.append(weight_file)
            elif nc is not None:
                print(f"⚠️ 类别数不匹配: {nc} (期望: {expected_nc})")
            else:
                print(f"❓ 无法确定类别数")
                
        except Exception as e:
            print(f"❌ 检查失败: {e}")
    
    return compatible_files

def main():
    """主函数"""
    print("🚀 权重文件检查工具\n")
    
    # 查找权重文件
    weight_files = find_weight_files()
    
    if not weight_files:
        print("❌ 未找到任何权重文件")
        print("\n💡 建议:")
        print("1. 下载官方预训练权重:")
        print("   - yolov8n.pt: https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt")
        print("   - yolov8s.pt: https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8s.pt")
        print("2. 或者使用YOLO自动下载功能")
        return
    
    # 分析每个权重文件
    for weight_file in weight_files:
        analyze_weight_file(weight_file)
    
    # 检查COCO128兼容性
    compatible_files = check_coco128_compatibility(weight_files)
    
    print(f"\n📋 总结:")
    print(f"   找到权重文件: {len(weight_files)}个")
    print(f"   COCO128兼容: {len(compatible_files)}个")
    
    if compatible_files:
        print(f"\n✅ 推荐使用的权重文件:")
        for file in compatible_files:
            print(f"   {os.path.basename(file)}")
    else:
        print(f"\n⚠️ 没有找到完全兼容COCO128的权重文件")
        print(f"建议下载官方预训练权重")

if __name__ == "__main__":
    main()
