#!/usr/bin/env python3
"""
测试知识蒸馏维度匹配修复
"""

import torch
import sys
import os

# 添加项目路径
sys.path.append('.')

from ultralytics import YOLO
from ultralytics.nn.tasks import DetectionModel
import Globals

def test_model_dimensions():
    """测试模型维度是否匹配"""
    print("🔧 测试知识蒸馏模型维度匹配...")
    
    try:
        # 使用修复后的配置
        model_config = "ultralytics/cfg/models/v8/yolov8-lite-fruit-fixed.yaml"
        
        print(f"📁 加载模型配置: {model_config}")
        
        # 创建模型
        model = DetectionModel(model_config, ch=3, nc=3)
        model.eval()
        
        print(f"✅ 模型创建成功")
        print(f"📊 模型层数: {len(model.model)}")
        
        # 测试前向传播
        test_input = torch.randn(1, 3, 640, 640)
        print(f"🧪 测试输入形状: {test_input.shape}")
        
        with torch.no_grad():
            output = model(test_input)
            
        print(f"✅ 前向传播成功")
        
        if isinstance(output, (list, tuple)):
            print(f"📊 输出类型: {type(output)}, 长度: {len(output)}")
            for i, out in enumerate(output):
                if hasattr(out, 'shape'):
                    print(f"   输出 {i}: {out.shape}")
                else:
                    print(f"   输出 {i}: {type(out)}")
        else:
            print(f"📊 输出形状: {output.shape}")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_distillation_config():
    """测试蒸馏配置"""
    print("\n🔧 测试蒸馏配置...")
    
    print(f"📋 当前配置:")
    print(f"   模型文件: {Globals.model_file}")
    print(f"   数据集: {Globals.dataset}")
    print(f"   蒸馏开启: {Globals.bool_distill}")
    print(f"   教师层列表: {Globals.teacher_peer_list}")
    print(f"   学生层列表: {Globals.student_peer_list}")
    print(f"   温度参数: {Globals.hyp_T}")
    
    # 检查配置一致性
    if len(Globals.teacher_peer_list) == len(Globals.student_peer_list):
        print(f"✅ 教师和学生层列表长度匹配: {len(Globals.teacher_peer_list)}")
    else:
        print(f"❌ 教师和学生层列表长度不匹配: {len(Globals.teacher_peer_list)} vs {len(Globals.student_peer_list)}")
        return False
        
    return True

def main():
    """主测试函数"""
    print("🚀 开始知识蒸馏维度匹配测试\n")
    
    # 测试配置
    config_ok = test_distillation_config()
    
    # 测试模型维度
    model_ok = test_model_dimensions()
    
    print(f"\n📋 测试结果:")
    print(f"   配置测试: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"   模型测试: {'✅ 通过' if model_ok else '❌ 失败'}")
    
    if config_ok and model_ok:
        print(f"\n🎉 所有测试通过！维度匹配问题已修复。")
        print(f"\n💡 建议:")
        print(f"   1. 使用 yolov8-lite-fruit-fixed.yaml 配置文件")
        print(f"   2. 确保预训练权重文件存在")
        print(f"   3. 开始训练前先进行小批量测试")
        return True
    else:
        print(f"\n❌ 测试失败，需要进一步调试。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
